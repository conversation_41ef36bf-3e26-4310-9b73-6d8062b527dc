@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  :root {
    --background: 0 0% 100%;
    --foreground: 215 25% 8%;
    --card: 210 40% 98%;
    --card-foreground: 215 25% 8%;
    --popover: 0 0% 100%;
    --popover-foreground: 215 25% 8%;
    --primary: 215 25% 8%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 215 25% 8%;
    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;
    --accent: 210 40% 96%;
    --accent-foreground: 215 25% 8%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 215 25% 8%;
    --success: 158 64% 52%;
    --success-foreground: 0 0% 100%;
    --warning: 43 96% 56%;
    --warning-foreground: 0 0% 100%;
    --info: 204 94% 94%;
    --info-foreground: 199 89% 48%;
    --radius: 0.5rem;
    --chart-1: 215 25% 8%;
    --chart-2: 158 64% 52%;
    --chart-3: 43 96% 56%;
    --chart-4: 0 84% 60%;
    --chart-5: 199 89% 48%;
  }
  .dark {
    --background: 215 25% 8%;
    --foreground: 210 40% 98%;
    --card: 215 28% 17%;
    --card-foreground: 210 40% 98%;
    --popover: 215 28% 17%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 215 25% 8%;
    --secondary: 215 28% 17%;
    --secondary-foreground: 210 40% 98%;
    --muted: 215 28% 17%;
    --muted-foreground: 217 10% 65%;
    --accent: 215 28% 17%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 63% 51%;
    --destructive-foreground: 210 40% 98%;
    --border: 215 28% 17%;
    --input: 215 28% 17%;
    --ring: 210 40% 98%;
    --success: 158 64% 52%;
    --success-foreground: 215 25% 8%;
    --warning: 43 96% 56%;
    --warning-foreground: 215 25% 8%;
    --info: 199 89% 48%;
    --info-foreground: 215 25% 8%;
    --radius: 0.5rem;
    --chart-1: 210 40% 98%;
    --chart-2: 158 64% 52%;
    --chart-3: 43 96% 56%;
    --chart-4: 0 63% 51%;
    --chart-5: 199 89% 48%;
  }
}

@layer components {
  /* Deep Blue Midnight Design System Utilities */
  
  /* Text Gradients */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-primary-900 bg-clip-text text-transparent;
  }

  .text-gradient-deep {
    @apply bg-gradient-to-r from-primary-700 to-primary-950 bg-clip-text text-transparent;
  }

  .text-gradient-subtle {
    @apply bg-gradient-to-r from-primary-500 to-primary-700 bg-clip-text text-transparent;
  }

  /* Glass Effects */
  .glass {
    @apply border border-border/50 bg-background/80 backdrop-blur-md;
  }

  .glass-deep {
    @apply border border-primary-900/20 bg-background/90 backdrop-blur-lg shadow-elegant;
  }

  .glass-card {
    @apply border border-border/30 bg-card/80 backdrop-blur-sm;
  }

  /* Background Gradients */
  .bg-gradient-deep {
    @apply bg-gradient-to-br from-primary-800 via-primary-900 to-primary-950;
  }

  .bg-gradient-subtle {
    @apply bg-gradient-to-br from-background via-card to-muted/30;
  }

  .bg-gradient-midnight {
    @apply bg-gradient-to-br from-primary-900 to-primary-950;
  }

  /* Enhanced Shadows */
  .shadow-elegant {
    @apply shadow-lg shadow-primary-900/10 dark:shadow-primary-950/20;
  }

  .shadow-glow {
    @apply shadow-xl shadow-primary-900/20 dark:shadow-primary-950/30;
  }

  .shadow-soft {
    @apply shadow-md shadow-primary-900/5 dark:shadow-primary-950/10;
  }

  .shadow-deep {
    @apply shadow-2xl shadow-primary-900/25 dark:shadow-primary-950/40;
  }

  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted)) hsl(var(--background));
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply rounded-full bg-muted-foreground;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-foreground;
  }

  /* Enhanced Form and UI Components */
  .btn-primary {
    @apply inline-flex items-center justify-center gap-2 rounded-lg bg-primary px-4 py-2.5 text-sm font-medium text-primary-foreground shadow-soft transition-all duration-200 hover:bg-primary/90 hover:shadow-elegant focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-[0.98];
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center gap-2 rounded-lg border border-primary bg-background px-4 py-2.5 text-sm font-medium text-primary shadow-soft transition-all duration-200 hover:bg-primary/5 hover:shadow-elegant focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-[0.98];
  }

  .btn-ghost {
    @apply inline-flex items-center justify-center gap-2 rounded-lg px-4 py-2.5 text-sm font-medium text-primary transition-all duration-200 hover:bg-primary/5 hover:shadow-soft focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-[0.98];
  }

  .btn-sm {
    @apply h-8 px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply h-12 px-6 py-3 text-base;
  }

  /* Enhanced Input Styles */
  .input {
    @apply flex h-10 w-full rounded-lg border border-input bg-background px-3 py-2 text-sm shadow-soft transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:border-primary disabled:cursor-not-allowed disabled:opacity-50;
  }

  .input-error {
    @apply border-destructive focus-visible:ring-destructive;
  }

  .input-success {
    @apply border-success focus-visible:ring-success;
  }

  /* Form Components */
  .form-group {
    @apply space-y-2;
  }

  .form-label {
    @apply text-sm font-medium text-foreground;
  }

  .form-error {
    @apply text-sm text-destructive;
  }

  .form-help {
    @apply text-xs text-muted-foreground;
  }

  .form-success {
    @apply text-sm text-success;
  }

  /* Enhanced Card Styles */
  .card {
    @apply rounded-xl border bg-card p-6 text-card-foreground shadow-soft transition-all duration-200 hover:shadow-elegant;
  }

  .card-elevated {
    @apply rounded-xl border bg-card p-6 text-card-foreground shadow-elegant hover:shadow-deep;
  }

  .card-glass {
    @apply rounded-xl border border-border/30 bg-card/80 p-6 text-card-foreground backdrop-blur-sm shadow-soft;
  }

  /* Modal Components */
  .modal-overlay {
    @apply fixed inset-0 z-50 flex items-center justify-center bg-primary-950/80 backdrop-blur-sm p-4;
  }

  .modal-content {
    @apply w-full max-w-lg rounded-xl border bg-background p-6 shadow-deep;
  }

  /* Loading and Animation */
  .spinner {
    @apply animate-spin rounded-full border-2 border-current border-t-transparent;
  }

  .pulse-subtle {
    @apply animate-pulse;
  }

  /* Interactive States */
  .interactive {
    @apply transition-all duration-200 hover:scale-[1.02] active:scale-[0.98];
  }

  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

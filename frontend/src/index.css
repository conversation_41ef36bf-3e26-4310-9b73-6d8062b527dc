@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  :root {
    --background: 210 25% 97%;
    --foreground: 210 65% 28%;
    --card: 210 30% 92%;
    --card-foreground: 210 65% 28%;
    --popover: 210 25% 97%;
    --popover-foreground: 210 65% 28%;
    --primary: 210 65% 28%;
    --primary-foreground: 210 25% 97%;
    --secondary: 210 35% 85%;
    --secondary-foreground: 210 65% 28%;
    --muted: 210 35% 85%;
    --muted-foreground: 210 55% 42%;
    --accent: 210 35% 85%;
    --accent-foreground: 210 65% 28%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --border: 210 40% 78%;
    --input: 210 40% 78%;
    --ring: 210 65% 28%;
    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;
    --warning: 45 93% 47%;
    --warning-foreground: 0 0% 100%;
    --info: 200 98% 39%;
    --info-foreground: 210 25% 97%;
    --radius: 0.5rem;
    --chart-1: 210 65% 28%;
    --chart-2: 142 76% 36%;
    --chart-3: 45 93% 47%;
    --chart-4: 0 84% 60%;
    --chart-5: 200 98% 39%;
    --chart-6: 280 100% 70%;
    --chart-7: 25 95% 53%;
    --chart-8: 180 100% 25%;
    --chart-9: 320 100% 68%;
    --chart-10: 60 100% 35%;
  }
  .dark {
    --background: 210 75% 15%;
    --foreground: 210 25% 97%;
    --card: 210 70% 22%;
    --card-foreground: 210 25% 97%;
    --popover: 210 70% 22%;
    --popover-foreground: 210 25% 97%;
    --primary: 210 25% 97%;
    --primary-foreground: 210 65% 28%;
    --secondary: 210 70% 22%;
    --secondary-foreground: 210 25% 97%;
    --muted: 210 70% 22%;
    --muted-foreground: 210 45% 65%;
    --accent: 210 70% 22%;
    --accent-foreground: 210 25% 97%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 25% 97%;
    --border: 210 60% 35%;
    --input: 210 60% 35%;
    --ring: 210 25% 97%;
    --success: 142 76% 36%;
    --success-foreground: 210 75% 15%;
    --warning: 45 93% 47%;
    --warning-foreground: 210 75% 15%;
    --info: 200 98% 39%;
    --info-foreground: 210 75% 15%;
    --radius: 0.5rem;
    --chart-1: 210 65% 28%;
    --chart-2: 142 76% 36%;
    --chart-3: 45 93% 47%;
    --chart-4: 0 84% 60%;
    --chart-5: 200 98% 39%;
    --chart-6: 280 100% 70%;
    --chart-7: 25 95% 53%;
    --chart-8: 180 100% 25%;
    --chart-9: 320 100% 68%;
    --chart-10: 60 100% 35%;
  }
}

@layer components {
  /* Deep Blue Midnight Design System Utilities */
  
  /* Text Gradients */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-primary-900 bg-clip-text text-transparent;
  }

  .text-gradient-deep {
    @apply bg-gradient-to-r from-primary-700 to-primary-950 bg-clip-text text-transparent;
  }

  .text-gradient-subtle {
    @apply bg-gradient-to-r from-primary-500 to-primary-700 bg-clip-text text-transparent;
  }

  /* Glass Effects */
  .glass {
    @apply border border-primary-600/20 bg-background/80 backdrop-blur-md;
  }

  .glass-deep {
    @apply border border-primary-800/30 bg-background/90 backdrop-blur-lg shadow-elegant;
  }

  .glass-card {
    @apply border border-primary-700/20 bg-card/80 backdrop-blur-sm shadow-soft;
  }

  /* Background Gradients */
  .bg-gradient-deep {
    @apply bg-gradient-to-br from-primary-800 via-primary-900 to-primary-950;
  }

  .bg-gradient-subtle {
    @apply bg-gradient-to-br from-background via-card to-muted/30;
  }

  .bg-gradient-midnight {
    @apply bg-gradient-to-br from-primary-900 to-primary-950;
  }

  /* Enhanced Shadows */
  .shadow-elegant {
    @apply shadow-lg shadow-primary-900/20 dark:shadow-primary-950/30;
  }

  .shadow-glow {
    @apply shadow-xl shadow-primary-800/25 dark:shadow-primary-900/35;
  }

  .shadow-soft {
    @apply shadow-md shadow-primary-800/10 dark:shadow-primary-900/15;
  }

  .shadow-deep {
    @apply shadow-2xl shadow-primary-900/30 dark:shadow-primary-950/40;
  }

  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted)) hsl(var(--background));
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-muted;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply rounded-full bg-muted-foreground;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-foreground;
  }

  /* Enhanced Form and UI Components */
  .btn-primary {
    @apply inline-flex items-center justify-center gap-2 rounded-lg bg-primary px-4 py-2.5 text-sm font-medium text-primary-foreground shadow-soft transition-all duration-200 hover:bg-primary/90 hover:shadow-elegant focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-[0.98];
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center gap-2 rounded-lg border border-primary bg-background px-4 py-2.5 text-sm font-medium text-primary shadow-soft transition-all duration-200 hover:bg-primary/5 hover:shadow-elegant focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-[0.98];
  }

  .btn-ghost {
    @apply inline-flex items-center justify-center gap-2 rounded-lg px-4 py-2.5 text-sm font-medium text-primary transition-all duration-200 hover:bg-primary/5 hover:shadow-soft focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-[0.98];
  }

  .btn-sm {
    @apply h-8 px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply h-12 px-6 py-3 text-base;
  }

  /* Enhanced Input Styles */
  .input {
    @apply flex h-10 w-full rounded-lg border border-input bg-background px-3 py-2 text-sm shadow-soft transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:border-primary disabled:cursor-not-allowed disabled:opacity-50;
  }

  .input-error {
    @apply border-destructive focus-visible:ring-destructive;
  }

  .input-success {
    @apply border-success focus-visible:ring-success;
  }

  /* Form Components */
  .form-group {
    @apply space-y-2;
  }

  .form-label {
    @apply text-sm font-medium text-foreground;
  }

  .form-error {
    @apply text-sm text-destructive;
  }

  .form-help {
    @apply text-xs text-muted-foreground;
  }

  .form-success {
    @apply text-sm text-success;
  }

  /* Enhanced Card Styles */
  .card {
    @apply rounded-xl border bg-card p-6 text-card-foreground shadow-soft transition-all duration-200 hover:shadow-elegant;
  }

  .card-elevated {
    @apply rounded-xl border bg-card p-6 text-card-foreground shadow-elegant hover:shadow-deep;
  }

  .card-glass {
    @apply rounded-xl border border-border/30 bg-card/80 p-6 text-card-foreground backdrop-blur-sm shadow-soft;
  }

  /* Modal Components */
  .modal-overlay {
    @apply fixed inset-0 z-50 flex items-center justify-center bg-primary-950/80 backdrop-blur-sm p-4;
  }

  .modal-content {
    @apply w-full max-w-lg rounded-xl border bg-background p-6 shadow-deep;
  }

  /* Loading and Animation */
  .spinner {
    @apply animate-spin rounded-full border-2 border-current border-t-transparent;
  }

  .pulse-subtle {
    @apply animate-pulse;
  }

  /* Interactive States */
  .interactive {
    @apply transition-all duration-200 hover:scale-[1.02] active:scale-[0.98];
  }

  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
